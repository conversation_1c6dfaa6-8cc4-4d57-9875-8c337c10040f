{"$schema": "https://schema.tauri.app/config/2", "productName": "ka<PERSON><PERSON>-crdt-printserver", "version": "0.1.0", "identifier": "com.kassierer-crdt-printserver.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "ka<PERSON><PERSON>-crdt-printserver", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}